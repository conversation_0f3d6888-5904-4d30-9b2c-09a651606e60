# Linux远程基线核查工具开发文档

## 📋 项目概述

### 项目名称
Linux Remote Baseline Checker (LRBC)

### 项目描述
一款基于 Tauri + React 技术栈的跨平台桌面应用，用于对Linux服务器进行远程安全基线核查。工具通过SSH连接远程Linux服务器，执行预定义的安全检查规则，生成详细的合规性报告。

### 核心特性
- 🔐 **SSH远程连接** - 支持密码和密钥认证
- 📊 **可视化界面** - 现代化React界面，操作简单直观
- 🛡️ **安全基线检查** - 内置多种安全检查规则
- 📄 **报告生成** - 支持HTML、PDF、JSON多种格式
- 🚀 **高性能** - Rust后端，响应速度快
- 📦 **单文件部署** - 无需安装，即开即用
- 🌐 **跨平台支持** - Windows、macOS、Linux全平台

## 🏗️ 技术架构

### 整体架构图
```
┌─────────────────────────────────────┐
│           React 前端界面             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │连接管理 │ │检查配置 │ │报告查看 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
├─────────────────────────────────────┤
│            IPC 通信层                │
├─────────────────────────────────────┤
│           Rust 后端核心              │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │SSH连接  │ │规则引擎 │ │报告生成 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
├─────────────────────────────────────┤
│          Linux 目标服务器            │
└─────────────────────────────────────┘
```

### 技术栈选择

#### 前端技术栈
- **框架**: React 18+ + TypeScript 5.9+
  - 支持最新的 Hooks API (useCallback, useMemo, useContext)
  - 组件优化使用 memo 和 useCallback
  - 自定义 Hooks 实现逻辑复用
- **UI组件库**: Ant Design 5.26.2+
  - 使用最新的 `items` 属性 API (Timeline, Menu, Select, Breadcrumb)
  - 支持 Quarter Picker (DatePicker)
  - 内置 showWeek 功能 (Calendar)
  - 推荐使用 options 数组而非 children 模式
- **状态管理**: Zustand 4.x
  - 轻量级、快速的状态管理方案
  - 基于 Hooks 的简洁 API
  - 无样板代码，支持中间件
- **路由**: React Router 6+
- **样式**: Tailwind CSS + Ant Design
- **图表**: ECharts for React

#### 后端技术栈
- **核心框架**: Tauri 2.x
  - 使用系统原生 WebView，体积小
  - 细粒度权限控制系统
  - 支持跨平台编译
- **编程语言**: Rust 1.70+
  - 内存安全，高性能
  - 丰富的系统编程库
- **SSH库**: ssh2 0.9+
  - 支持密码、公钥、SSH Agent 认证
  - 连接隧道和端口转发功能
  - 异步操作支持
- **序列化**: serde + serde_json
- **配置解析**: toml, yaml-rust
- **模板引擎**: tera
- **数据库**: SQLite (可选)

## 🚀 开发环境搭建

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Node.js**: 18.x 或更高版本 (推荐 20.x LTS)
- **Rust**: 1.70.0 或更高版本 (推荐最新稳定版)
- **TypeScript**: 5.9+ (支持最新语言特性)
- **内存**: 最少 4GB RAM (推荐 8GB)
- **存储**: 至少 2GB 可用空间

### 环境安装步骤

#### 1. 安装 Node.js
```bash
# 使用 nvm 安装 (推荐)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 或直接下载安装
# https://nodejs.org/
```

#### 2. 安装 Rust
```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

#### 3. 安装 Tauri CLI
```bash
# 安装 Tauri CLI
cargo install tauri-cli

# 或使用 npm
npm install -g @tauri-apps/cli
```

#### 4. 系统依赖 (Linux)
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install libwebkit2gtk-4.0-dev \
    build-essential \
    curl \
    wget \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev

# Fedora
sudo dnf groupinstall "C Development Tools and Libraries"
sudo dnf install webkit2gtk3-devel openssl-devel curl wget libappindicator-gtk3-devel librsvg2-devel
```

#### 5. 系统依赖 (macOS)
```bash
# 安装 Xcode Command Line Tools
xcode-select --install
```

## 📁 项目结构

```
linux-baseline-checker/
├── src/                          # React 前端源码
│   ├── components/               # React 组件
│   │   ├── ConnectionManager/    # 连接管理组件
│   │   ├── RuleConfig/          # 规则配置组件
│   │   ├── ReportViewer/        # 报告查看组件
│   │   └── common/              # 通用组件
│   ├── pages/                   # 页面组件
│   ├── hooks/                   # 自定义 Hooks
│   ├── utils/                   # 工具函数
│   ├── types/                   # TypeScript 类型定义
│   └── styles/                  # 样式文件
├── src-tauri/                   # Tauri 后端源码
│   ├── src/                     # Rust 源码
│   │   ├── commands/            # Tauri 命令
│   │   ├── ssh/                 # SSH 连接模块
│   │   ├── rules/               # 检查规则引擎
│   │   ├── reports/             # 报告生成模块
│   │   └── utils/               # 工具函数
│   ├── Cargo.toml              # Rust 依赖配置
│   └── tauri.conf.json         # Tauri 配置文件
├── rules/                       # 基线检查规则
│   ├── security/               # 安全相关规则
│   ├── system/                 # 系统配置规则
│   └── network/                # 网络安全规则
├── templates/                   # 报告模板
│   ├── html/                   # HTML 报告模板
│   └── json/                   # JSON 报告模板
├── docs/                       # 项目文档
├── tests/                      # 测试文件
├── package.json                # Node.js 依赖配置
├── tsconfig.json              # TypeScript 配置
├── tailwind.config.js         # Tailwind CSS 配置
└── README.md                  # 项目说明
```

## 🔧 核心功能模块设计

### 1. SSH连接管理模块
```rust
// src-tauri/src/ssh/connection.rs
use ssh2::Session;
use std::net::TcpStream;

pub struct SSHConnection {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub auth_method: AuthMethod,
    pub session: Option<Session>,
}

pub enum AuthMethod {
    Password(String),
    PrivateKey(String, Option<String>), // 私钥路径, 密码
    Agent,                              // SSH Agent 认证
    KeyboardInteractive,                // 交互式认证
}

impl SSHConnection {
    pub async fn connect(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let tcp = TcpStream::connect(format!("{}:{}", self.host, self.port))?;
        let mut session = Session::new()?;
        session.set_tcp_stream(tcp);
        session.handshake()?;

        match &self.auth_method {
            AuthMethod::Password(password) => {
                session.userauth_password(&self.username, password)?;
            }
            AuthMethod::PrivateKey(key_path, passphrase) => {
                session.userauth_pubkey_file(
                    &self.username,
                    None,
                    std::path::Path::new(key_path),
                    passphrase.as_deref(),
                )?;
            }
            AuthMethod::Agent => {
                session.userauth_agent(&self.username)?;
            }
            AuthMethod::KeyboardInteractive => {
                // 实现交互式认证逻辑
            }
        }

        self.session = Some(session);
        Ok(())
    }
}
```

### 2. 规则引擎模块
```rust
// src-tauri/src/rules/engine.rs
pub struct Rule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: RuleCategory,
    pub severity: Severity,
    pub command: String,
    pub expected: ExpectedResult,
}

pub enum RuleCategory {
    Security,
    System,
    Network,
    FileSystem,
}
```

### 3. 报告生成模块
```rust
// src-tauri/src/reports/generator.rs
pub struct CheckResult {
    pub rule_id: String,
    pub status: CheckStatus,
    pub actual_value: String,
    pub expected_value: String,
    pub message: String,
}

pub enum CheckStatus {
    Pass,
    Fail,
    Warning,
    Error,
}
```

## 🎨 用户界面设计

### 主要页面结构
1. **连接管理页面** - 管理SSH连接配置
   - 支持多种认证方式 (密码、密钥、Agent)
   - 连接测试和状态显示
2. **规则配置页面** - 选择和配置检查规则
   - 使用 Ant Design Select 的 options API
   - 规则分类和搜索功能
3. **执行检查页面** - 实时显示检查进度
   - 使用 Timeline 组件显示进度
   - 实时状态更新
4. **报告查看页面** - 查看和导出检查结果
   - 使用 Calendar 组件 (showWeek 功能)
   - 支持多种导出格式

### UI组件规范
- 使用 Ant Design 5.26+ 最新 API
- 推荐使用 items/options 属性而非 children
- 响应式设计，适配不同屏幕尺寸
- 深色/浅色主题切换支持
- 国际化支持 (中文/英文)
- 组件优化使用 React.memo 和 useCallback

## 📝 开发指南

### 项目初始化
```bash
# 创建 Tauri 项目
npm create tauri-app@latest linux-baseline-checker
cd linux-baseline-checker

# 安装前端依赖
npm install

# 安装最新版本的核心依赖
npm install antd@^5.26.2 @ant-design/icons zustand@^4.5.0 react-router-dom@^6.0.0
npm install -D typescript@^5.9.0 tailwindcss @types/node

# 添加 Rust 依赖到 src-tauri/Cargo.toml
# ssh2 = "0.9"
# serde = { version = "1.0", features = ["derive"] }
# tokio = { version = "1.0", features = ["full"] }

# 启动开发服务器
npm run tauri dev
```

### 开发流程
1. **前端开发**: 在 `src/` 目录下开发 React 组件
   - 使用 TypeScript 5.9+ 严格模式
   - 遵循 React 18+ 最佳实践 (useCallback, memo)
   - Ant Design 5.26+ 组件使用 items 属性 API
2. **后端开发**: 在 `src-tauri/src/` 目录下开发 Rust 代码
   - 使用 ssh2 库实现多种认证方式
   - 实现细粒度的权限控制
3. **调试测试**: 使用 `npm run tauri dev` 启动开发模式
4. **构建发布**: 使用 `npm run tauri build` 构建生产版本

### 代码规范
- **TypeScript**: 5.9+ 严格模式，完整类型定义，使用最新语言特性
- **React**: 使用 Hooks API，组件优化 (memo, useCallback)，自定义 Hooks
- **Ant Design**: 使用推荐的 items/options API，避免废弃的 children 模式
- **Zustand**: 简洁的状态管理，避免样板代码
- **Rust**: 使用 `rustfmt` 格式化，`clippy` 检查
- **提交规范**: 使用 Conventional Commits 格式
- **测试覆盖**: 核心功能必须有单元测试

## 🧪 测试策略

### 测试类型
- **单元测试**: Rust 后端逻辑测试
- **集成测试**: SSH连接和规则执行测试
- **E2E测试**: 完整用户流程测试
- **性能测试**: 大量服务器并发测试

### 测试工具
- **Rust测试**: 内置 `cargo test`
- **前端测试**: Jest + React Testing Library
- **E2E测试**: Playwright

## 📦 构建和部署

### 构建命令
```bash
# 开发构建
npm run tauri dev

# 生产构建
npm run tauri build

# 构建特定平台
npm run tauri build -- --target x86_64-pc-windows-msvc
npm run tauri build -- --target x86_64-apple-darwin
npm run tauri build -- --target x86_64-unknown-linux-gnu
```

### 发布包结构
```
target/release/bundle/
├── deb/                    # Linux .deb 包
├── appimage/              # Linux AppImage
├── msi/                   # Windows .msi 安装包
├── nsis/                  # Windows NSIS 安装包
└── macos/                 # macOS .app 和 .dmg
```

## 🔒 安全考虑

### 数据安全
- SSH密码和私钥加密存储
- 敏感信息不记录到日志
- 报告数据本地存储，不上传云端

### 权限控制
- 最小权限原则，只请求必要的系统权限
- Tauri权限配置严格限制API调用
- 用户数据隔离存储

## 🚀 性能优化

### 前端优化
- 组件懒加载
- 虚拟滚动处理大量数据
- 防抖节流优化用户交互

### 后端优化
- SSH连接池复用
- 并发执行检查规则
- 结果缓存机制

## 📋 开发里程碑

### Phase 1: 基础框架 (2周)
- [x] 项目初始化和环境搭建
- [ ] 基础UI框架搭建
- [ ] SSH连接功能实现
- [ ] 基本的规则执行引擎

### Phase 2: 核心功能 (3周)
- [ ] 完整的规则引擎实现
- [ ] 报告生成功能
- [ ] 用户界面完善
- [ ] 基础测试覆盖

### Phase 3: 高级功能 (2周)
- [ ] 批量服务器管理
- [ ] 自定义规则支持
- [ ] 报告模板定制
- [ ] 性能优化

### Phase 4: 发布准备 (1周)
- [ ] 全面测试
- [ ] 文档完善
- [ ] 打包发布
- [ ] 用户手册

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码审查标准
- 代码符合项目规范
- 包含必要的测试
- 文档更新完整
- 通过所有CI检查

## 🐛 问题排查

### 常见问题

#### 1. Tauri开发环境问题
```bash
# 检查Rust版本
rustc --version

# 更新Rust
rustup update

# 重新安装Tauri CLI
cargo install tauri-cli --force
```

#### 2. SSH连接失败
- 检查网络连通性
- 验证SSH服务状态
- 确认认证信息正确
- 检查防火墙设置

#### 3. 构建失败
```bash
# 清理构建缓存
cargo clean
npm run tauri build

# 检查依赖版本
cargo tree
npm list
```

## 📊 性能基准

### 目标性能指标
- **启动时间**: < 3秒
- **SSH连接**: < 5秒
- **单服务器检查**: < 30秒
- **内存占用**: < 100MB
- **安装包大小**: < 20MB

### 性能测试
```bash
# 运行性能测试
cargo test --release performance_tests

# 内存使用分析
valgrind --tool=massif target/release/linux-baseline-checker
```

## 📚 参考资料

### 官方文档
- [Tauri官方文档](https://tauri.app/)
- [React官方文档](https://react.dev/)
- [Ant Design文档](https://ant.design/)
- [Rust官方文档](https://doc.rust-lang.org/)

### 技术资源
- [SSH2-rs文档](https://docs.rs/ssh2/) - 支持多种认证方式
- [Serde序列化](https://serde.rs/)
- [Tera模板引擎](https://tera.netlify.app/)
- [Zustand状态管理](https://zustand.pmnd.rs/) - 轻量级状态管理
- [TypeScript 5.9 新特性](https://www.typescriptlang.org/docs/)
- [React 18 Hooks 指南](https://react.dev/reference/react/hooks)
- [Ant Design 5.26 组件库](https://ant.design/components/overview/)

### 安全基线参考
- [CIS Linux Benchmark](https://www.cisecurity.org/benchmark/linux)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [等保2.0技术要求](https://www.tc260.org.cn/)

## 📞 支持与联系

### 技术支持
- **邮箱**: <EMAIL>
- **文档**: [项目Wiki](https://github.com/example/linux-baseline-checker/wiki)
- **问题反馈**: [GitHub Issues](https://github.com/example/linux-baseline-checker/issues)

### 社区
- **讨论区**: [GitHub Discussions](https://github.com/example/linux-baseline-checker/discussions)
- **QQ群**: 123456789
- **微信群**: 扫码加入

---

**版本**: v1.0.0
**最后更新**: 2024-12-22
**文档维护**: 开发团队
**许可证**: MIT License
